#!/usr/bin/env python3
"""
AI Vision Platform 自包含应用打包脚本
创建一个完全独立的应用，用户无需安装Python环境即可使用
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path
import json

# 项目根目录
PROJECT_ROOT = Path(__file__).resolve().parent
BACKEND_DIR = PROJECT_ROOT / "Backend_Django"
FRONTEND_DIR = PROJECT_ROOT / "Frontend"

def check_requirements():
    """检查打包环境要求"""
    print("=== 检查打包环境 ===")
    
    # 检查Python版本
    if sys.version_info < (3, 9):
        print("✗ Python版本过低，需要Python 3.9或更高版本")
        return False
    print(f"✓ Python版本: {sys.version}")
    
    # 检查Node.js
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ Node.js版本: {result.stdout.strip()}")
        else:
            print("✗ Node.js未安装")
            return False
    except FileNotFoundError:
        print("✗ Node.js未安装")
        return False
    
    # 检查npm
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True, shell=True)
        if result.returncode == 0:
            print(f"✓ npm版本: {result.stdout.strip()}")
        else:
            print("✗ npm未安装")
            return False
    except FileNotFoundError:
        print("✗ npm未安装")
        return False
    
    # 检查目录结构
    if not BACKEND_DIR.exists():
        print(f"✗ 后端目录不存在: {BACKEND_DIR}")
        return False
    print(f"✓ 后端目录: {BACKEND_DIR}")
    
    if not FRONTEND_DIR.exists():
        print(f"✗ 前端目录不存在: {FRONTEND_DIR}")
        return False
    print(f"✓ 前端目录: {FRONTEND_DIR}")
    
    return True

def install_backend_dependencies():
    """安装后端依赖"""
    print("\n=== 安装后端依赖 ===")
    
    # 切换到后端目录
    os.chdir(BACKEND_DIR)
    
    # 检查虚拟环境
    venv_dir = BACKEND_DIR / "venv"
    if not venv_dir.exists():
        print("创建虚拟环境...")
        subprocess.check_call([sys.executable, "-m", "venv", "venv"])
    
    # 激活虚拟环境并安装依赖
    if sys.platform == "win32":
        python_exe = venv_dir / "Scripts" / "python.exe"
        pip_exe = venv_dir / "Scripts" / "pip.exe"
    else:
        python_exe = venv_dir / "bin" / "python"
        pip_exe = venv_dir / "bin" / "pip"
    
    print("安装Python依赖...")
    # 使用python -m pip来避免Windows上的pip升级问题
    try:
        subprocess.check_call([str(python_exe), "-m", "pip", "install", "--upgrade", "pip"])
    except subprocess.CalledProcessError:
        print("警告: pip升级失败，继续使用当前版本")

    subprocess.check_call([str(python_exe), "-m", "pip", "install", "-r", "requirements.txt"])
    subprocess.check_call([str(python_exe), "-m", "pip", "install", "pyinstaller"])
    
    print("✓ 后端依赖安装完成")
    return python_exe

def build_backend_executable(python_exe):
    """构建后端可执行文件"""
    print("\n=== 构建后端可执行文件 ===")
    
    # 切换到后端目录
    os.chdir(BACKEND_DIR)
    
    # 运行构建脚本
    print("运行PyInstaller构建...")
    subprocess.check_call([str(python_exe), "build_backend.py"])
    
    # 检查构建结果
    dist_dir = BACKEND_DIR / "dist" / "django_server"
    if not dist_dir.exists():
        raise Exception("后端可执行文件构建失败")
    
    print(f"✓ 后端可执行文件构建完成: {dist_dir}")
    return dist_dir

def install_frontend_dependencies():
    """安装前端依赖"""
    print("\n=== 安装前端依赖 ===")
    
    # 切换到前端目录
    os.chdir(FRONTEND_DIR)
    
    # 安装npm依赖
    print("安装npm依赖...")
    subprocess.check_call(["npm", "install"])
    
    print("✓ 前端依赖安装完成")

def build_frontend():
    """构建前端"""
    print("\n=== 构建前端 ===")
    
    # 切换到前端目录
    os.chdir(FRONTEND_DIR)
    
    # 构建前端
    print("构建React应用...")
    subprocess.check_call(["npm", "run", "build"])
    
    # 检查构建结果
    dist_dir = FRONTEND_DIR / "dist"
    if not dist_dir.exists():
        raise Exception("前端构建失败")
    
    print(f"✓ 前端构建完成: {dist_dir}")
    return dist_dir

def package_electron_app():
    """打包Electron应用"""
    print("\n=== 打包Electron应用 ===")
    
    # 切换到前端目录
    os.chdir(FRONTEND_DIR)
    
    # 检查后端可执行文件是否存在
    backend_exe_dir = BACKEND_DIR / "dist" / "django_server"
    if not backend_exe_dir.exists():
        raise Exception("后端可执行文件不存在，请先构建后端")
    
    # 打包Electron应用
    print("打包Electron应用...")
    subprocess.check_call(["npm", "run", "electron:build:win"])
    
    # 检查打包结果
    release_dir = FRONTEND_DIR / "release"
    if not release_dir.exists():
        raise Exception("Electron应用打包失败")
    
    print(f"✓ Electron应用打包完成: {release_dir}")
    return release_dir

def create_user_guide():
    """创建用户使用指南"""
    guide_content = """# AI Vision Platform 使用指南

## 🎯 关于此应用
这是一个完全自包含的AI视觉处理平台，无需安装Python或其他依赖即可直接使用。

## 🚀 快速开始

### 安装版本
1. 运行 `AI Vision App-1.0.0-x64.exe` 安装程序
2. 按照提示完成安装
3. 双击桌面快捷方式启动应用

### 便携版本
1. 解压 `win-unpacked` 文件夹到任意位置
2. 双击 `AI Vision App.exe` 启动应用

## 📋 系统要求
- Windows 10 或更高版本
- 至少 4GB 内存
- 至少 2GB 可用磁盘空间

## 🔧 故障排除

### 应用启动缓慢
- 首次启动可能需要1-2分钟，这是正常现象
- 后续启动会更快

### 后端服务启动失败
1. 检查防火墙设置，确保允许应用访问网络
2. 确保端口8000未被其他程序占用
3. 查看日志文件：`%APPDATA%\\ai-vision-platform\\app.log`

### 获取帮助
如遇到问题，请查看应用日志文件或联系技术支持。

## 📁 应用数据位置
- 配置文件：`%APPDATA%\\ai-vision-platform\\`
- 日志文件：`%APPDATA%\\ai-vision-platform\\app.log`
- 用户数据：应用安装目录下的 `data` 文件夹
"""
    
    guide_file = PROJECT_ROOT / "用户使用指南.md"
    with open(guide_file, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"✓ 用户指南已创建: {guide_file}")

def main():
    """主函数"""
    print("=== AI Vision Platform 自包含应用打包工具 ===")
    print(f"项目根目录: {PROJECT_ROOT}")
    
    try:
        # 1. 检查环境
        if not check_requirements():
            print("\n✗ 环境检查失败，请解决上述问题后重试")
            sys.exit(1)
        
        # 2. 安装后端依赖
        python_exe = install_backend_dependencies()
        
        # 3. 构建后端可执行文件
        backend_dist = build_backend_executable(python_exe)
        
        # 4. 安装前端依赖
        install_frontend_dependencies()
        
        # 5. 构建前端
        frontend_dist = build_frontend()
        
        # 6. 打包Electron应用
        release_dir = package_electron_app()
        
        # 7. 创建用户指南
        create_user_guide()
        
        print("\n=== 打包完成 ===")
        print(f"✓ 打包文件位置: {release_dir}")
        print("✓ 应用已完全自包含，用户无需安装Python环境")
        print("\n📦 分发文件:")
        
        # 列出分发文件
        for item in release_dir.iterdir():
            if item.is_file() and item.suffix == '.exe':
                print(f"  - {item.name} (安装程序)")
            elif item.is_dir() and 'unpacked' in item.name:
                print(f"  - {item.name}/ (便携版本)")
        
        print(f"\n📖 用户指南: {PROJECT_ROOT / '用户使用指南.md'}")
        
    except Exception as e:
        print(f"\n✗ 打包失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
