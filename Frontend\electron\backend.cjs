const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { app } = require('electron');

// --- 日志记录设置 (与main.cjs共享或类似逻辑) ---
// 注意：在backend.cjs中直接使用app.getPath可能不合适，因为它可能在app准备好之前被调用
// 一个更健壮的方法是从main.cjs传递logPath或log函数
// 这里为了简化，我们假设app已经ready或采用一个固定的后备路径
let logFilePath;
try {
  const userDataPath = app ? app.getPath('userData') : path.join(__dirname, 'logs'); // 后备路径
  if (app && !fs.existsSync(userDataPath)) {
    fs.mkdirSync(userDataPath, { recursive: true });
  }
  logFilePath = path.join(userDataPath, 'backend.log');
} catch (e) {
  // 如果在测试环境或者app未初始化，使用本地日志
  const logsDir = path.join(__dirname, 'logs');
  if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
  }
  logFilePath = path.join(logsDir, 'backend.log');
  console.error('Could not get userDataPath, using local backend.log', e);
}

function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `${timestamp}: ${message}\n`;
  console.log(`(Backend Log): ${message}`);
  try {
    fs.appendFileSync(logFilePath, logMessage);
  } catch (err) {
    console.error('Failed to write to backend log file:', err);
  }
}

// 清理旧日志
try {
  if (fs.existsSync(logFilePath)) {
    fs.unlinkSync(logFilePath); 
  }
} catch (err) {
  console.error('Failed to delete old backend log file:', err);
}
log('Backend script initialized.');
// --- 日志记录设置结束 ---

const isDev = process.env.NODE_ENV === 'development';
let backendProcess = null;

// 查找Python可执行文件的函数
function findPythonExecutable() {
  log('Searching for Python executable...');

  const possiblePaths = [
    // 系统Python路径（最常用）
    'python',
    'python3',
    // 用户系统的具体路径
    'C:\\Python313\\python.exe',
    'C:\\Python312\\python.exe',
    // 其他常见安装路径
    'C:\\Python39\\python.exe',
    'C:\\Python310\\python.exe',
    'C:\\Python311\\python.exe',
    // WindowsApps路径
    path.join(process.env.USERPROFILE, 'AppData\\Local\\Microsoft\\WindowsApps\\python.exe'),
    // 用户目录安装
    path.join(process.env.USERPROFILE, 'AppData\\Local\\Programs\\Python\\Python39\\python.exe'),
    path.join(process.env.USERPROFILE, 'AppData\\Local\\Programs\\Python\\Python310\\python.exe'),
    path.join(process.env.USERPROFILE, 'AppData\\Local\\Programs\\Python\\Python311\\python.exe'),
    path.join(process.env.USERPROFILE, 'AppData\\Local\\Programs\\Python\\Python312\\python.exe'),
    path.join(process.env.USERPROFILE, 'AppData\\Local\\Programs\\Python\\Python313\\python.exe')
  ];

  // 首先尝试系统Python命令
  for (const pythonCmd of ['python', 'python3']) {
    try {
      const { execSync } = require('child_process');
      const version = execSync(`${pythonCmd} --version`, { encoding: 'utf8', timeout: 5000 });
      if (version.includes('Python 3.')) {
        log(`Found system Python command: ${pythonCmd} (${version.trim()})`);
        return pythonCmd;
      }
    } catch (error) {
      log(`System Python command ${pythonCmd} not available: ${error.message}`);
    }
  }

  // 然后尝试具体的文件路径
  for (const pythonPath of possiblePaths.slice(2)) {
    try {
      if (fs.existsSync(pythonPath)) {
        // 验证这是一个有效的Python可执行文件
        try {
          const { execSync } = require('child_process');
          const version = execSync(`"${pythonPath}" --version`, { encoding: 'utf8', timeout: 5000 });
          if (version.includes('Python 3.')) {
            log(`Found and verified Python at: ${pythonPath} (${version.trim()})`);
        return pythonPath;
          }
        } catch (verifyError) {
          log(`Python at ${pythonPath} exists but failed verification: ${verifyError.message}`);
          continue;
        }
      }
    } catch (error) {
      log(`Error checking Python path ${pythonPath}: ${error.message}`);
      continue;
    }
  }

  // 如果都没找到，尝试使用where命令查找
  try {
    const { execSync } = require('child_process');
    const whereResult = execSync('where python', { encoding: 'utf8', timeout: 5000 });
    const paths = whereResult.trim().split('\n');
    for (const foundPath of paths) {
      const cleanPath = foundPath.trim();
      if (cleanPath && fs.existsSync(cleanPath)) {
        try {
          const version = execSync(`"${cleanPath}" --version`, { encoding: 'utf8', timeout: 5000 });
          if (version.includes('Python 3.')) {
            log(`Found Python via 'where' command: ${cleanPath} (${version.trim()})`);
            return cleanPath;
          }
        } catch (verifyError) {
          log(`Python found via 'where' at ${cleanPath} failed verification: ${verifyError.message}`);
        }
      }
    }
  } catch (whereError) {
    log(`'where python' command failed: ${whereError.message}`);
  }

  log('No Python executable found, using fallback');
  return 'python'; // 最后回退
}

function startBackend() {
  return new Promise((resolve, reject) => {
    try {
      log('Starting backend service attempt...');
      const rootDir = path.resolve(__dirname, '../../');

      let backendExecutable, backendArgs;

      if (isDev) {
        // 开发环境：使用Python + manage.py
        const pythonExecutableName = process.platform === 'win32' ? 'python.exe' : 'python';
        const pythonPath = path.join(rootDir, 'Backend_Django/venv/Scripts', pythonExecutableName);
        const scriptPath = path.join(rootDir, 'Backend_Django/manage.py');

        log(`Development mode - Python path: ${pythonPath}`);
        log(`Development mode - Script path: ${scriptPath}`);

        // 检查开发环境文件
        if (!fs.existsSync(pythonPath)) {
          const errorMsg = `Python interpreter not found at: ${pythonPath}`;
          log(errorMsg);
          return reject(new Error(errorMsg));
        }

        if (!fs.existsSync(scriptPath)) {
          const errorMsg = `Django manage.py not found at: ${scriptPath}`;
          log(errorMsg);
          return reject(new Error(errorMsg));
        }

        backendExecutable = pythonPath;
        backendArgs = [scriptPath, 'runserver', '127.0.0.1:8000', '--noreload', '--skip-checks'];

      } else {
        // 生产环境：使用PyInstaller打包的可执行文件
        const executableName = process.platform === 'win32' ? 'django_server.exe' : 'django_server';
        const executablePath = path.join(process.resourcesPath, 'Backend_Django', 'dist', 'django_server', executableName);

        log(`Production mode - Executable path: ${executablePath}`);

        // 检查可执行文件是否存在
        if (!fs.existsSync(executablePath)) {
          const errorMsg = `Django executable not found at: ${executablePath}. Please build the backend first using build_backend.py`;
          log(errorMsg);
          return reject(new Error(errorMsg));
        }

        backendExecutable = executablePath;
        backendArgs = [];
      }

      log(`Environment: ${isDev ? 'development' : 'production'}`);
      log(`Backend executable: ${backendExecutable}`);
      log(`Backend arguments: ${backendArgs.join(' ')}`);

      log('Spawning Django process...');
      backendProcess = spawn(backendExecutable, backendArgs, {
        stdio: 'pipe',
        env: {
          ...process.env,
          PYTHONUNBUFFERED: '1',
          DJANGO_DEBUG: isDev ? 'True' : 'False'
        }
      });

      backendProcess.stdout.on('data', (data) => {
        const output = data.toString();
        log(`Backend stdout: ${output.trim()}`);

        // 检查Django服务启动成功的标志
        if (output.includes('Starting development server at') ||
            output.includes('Quit the server with CTRL-BREAK') ||
            output.includes('Django version') ||
            output.includes('Starting server at')) {
          log('Django service confirmed running.');
          clearTimeout(startupTimeout);
          resolve();
        }
      });

      backendProcess.stderr.on('data', (data) => {
        const error = data.toString();
        log(`Backend stderr: ${error.trim()}`);
        // 不要因为警告而拒绝，除非是非常明确的错误指示
      });

      backendProcess.on('error', (error) => {
        log(`Backend process spawn error: ${error.stack || error}`);
        reject(error);
      });

      backendProcess.on('exit', (code, signal) => {
        log(`Backend process exited with code ${code} and signal ${signal}`);
        if (code !== 0 && code !== null) { // null code can happen if killed successfully
          const errorMsg = `Backend process exited unexpectedly with code ${code}.`;
          log(errorMsg); // Log it but don't necessarily reject promise here if startup was already resolved
        }
      });

      const timeout = isDev ? 45000 : 120000; // 生产环境2分钟超时
      log(`Setting backend startup timeout to ${timeout / 1000} seconds.`);
      const startupTimeout = setTimeout(() => {
        // 检查进程是否真的在运行，或者只是挂起
        if (backendProcess && !backendProcess.killed) {
            log(`Backend startup timeout (${timeout/1000}s). Attempting to kill process.`);
            // 尝试发送信号，如果进程未响应，可能需要更强制的杀死
            if (process.platform === 'win32') {
                spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
            } else {
                backendProcess.kill('SIGKILL'); // 更强制的信号
            }
            reject(new Error(`Backend startup timed out after ${timeout/1000} seconds. This may be due to missing dependencies or system resources. Please check the backend log for details.`));
        } else if (!backendProcess) {
             reject(new Error('Backend startup timed out. Process was not initiated or already exited.'));
        }
      }, timeout);

      // 如果resolve了，清除超时
      backendProcess.stdout.once('data', (data) => {
        if (data.toString().includes('Starting development server at') || data.toString().includes('Quit the server with CTRL-BREAK')) {
          clearTimeout(startupTimeout);
        }
      });

    } catch (error) {
      log(`Failed to start backend service: ${error.stack || error}`);
      reject(error);
    }
  });
}

function stopBackend() {
  return new Promise((resolve) => {
    if (backendProcess && !backendProcess.killed) {
      log('Stopping backend service...');
      if (process.platform === 'win32') {
        log(`Killing backend process tree (PID: ${backendProcess.pid}) on Windows.`);
        spawn('taskkill', ['/pid', backendProcess.pid, '/f', '/t']);
      } else {
        log(`Sending SIGTERM to backend process (PID: ${backendProcess.pid}) on ${process.platform}.`);
        backendProcess.kill('SIGTERM');
        // Set a timeout to SIGKILL if it doesn't terminate gracefully
        setTimeout(() => {
            if (backendProcess && !backendProcess.killed) {
                log(`Backend process (PID: ${backendProcess.pid}) did not terminate with SIGTERM, sending SIGKILL.`);
                backendProcess.kill('SIGKILL');
            }
        }, 5000); // 5 seconds to terminate gracefully
      }
      backendProcess = null; // Clear immediately, actual process termination is async
      log('Backend service stop command issued.');
    }
    resolve();
  });
}

module.exports = {
  startBackend,
  stopBackend
}; 