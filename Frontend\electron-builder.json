{"appId": "com.aivision.app", "productName": "AI Vision App", "directories": {"output": "release"}, "files": ["dist/**/*", "electron/**/*", "package.json", "!node_modules/**/*", "!src/**/*", "!public/**/*"], "asar": false, "extraResources": [{"from": "../Backend_Django/dist/django_server", "to": "Backend_Django/dist/django_server", "filter": ["**/*"]}, {"from": "../Backend_Django/models", "to": "Backend_Django/models", "filter": ["**/*"]}, {"from": "../Backend_Django/db", "to": "Backend_Django/db", "filter": ["**/*"]}], "win": {"icon": "public/favicon.ico", "target": [{"target": "nsis", "arch": ["x64"]}], "forceCodeSigning": false, "verifyUpdateCodeSignature": false}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "AI Vision App", "runAfterFinish": true}, "compression": "normal", "npmRebuild": false, "nodeGypRebuild": false}