#!/usr/bin/env python3
"""
Django应用PyInstaller打包脚本
用于创建自包含的Django可执行文件，无需用户安装Python环境
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

# 获取项目根目录
BASE_DIR = Path(__file__).resolve().parent
PROJECT_ROOT = BASE_DIR.parent

def install_pyinstaller():
    """安装PyInstaller"""
    print("检查PyInstaller...")
    try:
        import PyInstaller
        print(f"✓ PyInstaller已安装: {PyInstaller.__version__}")
    except ImportError:
        print("安装PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✓ PyInstaller安装完成")

def create_django_entry_script():
    """创建Django启动脚本"""
    entry_script = BASE_DIR / "django_server.py"
    
    script_content = '''#!/usr/bin/env python3
"""
Django服务器启动脚本 - 用于PyInstaller打包
"""
import os
import sys
import django
from django.core.management import execute_from_command_line
from django.core.wsgi import get_wsgi_application

# 设置Django设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend_project.settings')

def main():
    """主函数"""
    try:
        # 初始化Django
        django.setup()
        
        # 执行数据库迁移
        print("执行数据库迁移...")
        execute_from_command_line(['manage.py', 'migrate', '--noinput'])
        
        # 启动开发服务器
        print("启动Django服务器...")
        execute_from_command_line(['manage.py', 'runserver', '127.0.0.1:8000', '--noreload'])
        
    except Exception as e:
        print(f"Django服务器启动失败: {e}")
        input("按任意键退出...")
        sys.exit(1)

if __name__ == '__main__':
    main()
'''
    
    with open(entry_script, 'w', encoding='utf-8') as f:
        f.write(script_content)
    
    print(f"✓ 创建Django启动脚本: {entry_script}")
    return entry_script

def create_pyinstaller_spec():
    """创建PyInstaller配置文件"""
    spec_file = BASE_DIR / "django_server.spec"
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import os
from pathlib import Path

# 项目路径
BASE_DIR = Path(r"{BASE_DIR}")
PROJECT_ROOT = Path(r"{PROJECT_ROOT}")

# 收集Django相关文件
django_datas = []

# 添加Django模板文件
templates_dir = BASE_DIR / "vision_app" / "templates"
if templates_dir.exists():
    django_datas.append((str(templates_dir), "vision_app/templates"))

# 添加静态文件
static_dirs = [
    BASE_DIR / "vision_app" / "static",
    BASE_DIR / "backend_project" / "static"
]
for static_dir in static_dirs:
    if static_dir.exists():
        django_datas.append((str(static_dir), str(static_dir.relative_to(BASE_DIR))))

# 添加模型文件目录
models_dir = BASE_DIR / "models"
if models_dir.exists():
    django_datas.append((str(models_dir), "models"))

# 添加数据库文件
db_dir = BASE_DIR / "db"
if db_dir.exists():
    django_datas.append((str(db_dir), "db"))

# 添加Django迁移文件
migrations_dir = BASE_DIR / "vision_app" / "migrations"
if migrations_dir.exists():
    django_datas.append((str(migrations_dir), "vision_app/migrations"))

# 隐藏导入
hiddenimports = [
    'django.core.management',
    'django.core.management.commands',
    'django.core.management.commands.runserver',
    'django.core.management.commands.migrate',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'corsheaders',
    'rest_framework',
    'vision_app',
    'vision_app.apps',
    'vision_app.models',
    'vision_app.views',
    'vision_app.urls',
    'backend_project.settings',
    'backend_project.urls',
    'backend_project.wsgi',
    # AI相关包
    'torch',
    'torchvision',
    'onnxruntime',
    'paddlepaddle',
    'paddleocr',
    'ultralytics',
    'cv2',
    'numpy',
    'PIL',
    'sklearn',
    'scipy',
    'matplotlib',
    'pandas'
]

a = Analysis(
    ['django_server.py'],
    pathex=[str(BASE_DIR)],
    binaries=[],
    datas=django_datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='django_server',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='django_server'
)
'''
    
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print(f"✓ 创建PyInstaller配置文件: {spec_file}")
    return spec_file

def build_executable():
    """构建可执行文件"""
    print("开始构建Django可执行文件...")
    
    # 切换到Backend_Django目录
    os.chdir(BASE_DIR)
    
    # 运行PyInstaller
    spec_file = "django_server.spec"
    cmd = [sys.executable, "-m", "PyInstaller", "--clean", spec_file]
    
    print(f"执行命令: {' '.join(cmd)}")
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode == 0:
        print("✓ Django可执行文件构建成功!")
        dist_dir = BASE_DIR / "dist" / "django_server"
        print(f"输出目录: {dist_dir}")
        return dist_dir
    else:
        print("✗ 构建失败:")
        print(result.stdout)
        print(result.stderr)
        return None

def main():
    """主函数"""
    print("=== Django应用PyInstaller打包工具 ===")
    print(f"项目目录: {BASE_DIR}")
    
    try:
        # 1. 安装PyInstaller
        install_pyinstaller()
        
        # 2. 创建Django启动脚本
        entry_script = create_django_entry_script()
        
        # 3. 创建PyInstaller配置
        spec_file = create_pyinstaller_spec()
        
        # 4. 构建可执行文件
        dist_dir = build_executable()
        
        if dist_dir:
            print("\n=== 构建完成 ===")
            print(f"可执行文件位置: {dist_dir / 'django_server.exe'}")
            print("现在可以将整个dist/django_server目录复制到目标机器上运行")
        else:
            print("\n=== 构建失败 ===")
            sys.exit(1)
            
    except Exception as e:
        print(f"构建过程中出现错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
