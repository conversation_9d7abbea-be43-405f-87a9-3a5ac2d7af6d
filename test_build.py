#!/usr/bin/env python3
"""
测试构建过程的脚本
验证自包含应用打包是否正常工作
"""

import os
import sys
import subprocess
from pathlib import Path
import time

PROJECT_ROOT = Path(__file__).resolve().parent
BACKEND_DIR = PROJECT_ROOT / "Backend_Django"
FRONTEND_DIR = PROJECT_ROOT / "Frontend"

def test_backend_build():
    """测试后端构建"""
    print("=== 测试后端构建 ===")
    
    # 检查构建脚本是否存在
    build_script = BACKEND_DIR / "build_backend.py"
    if not build_script.exists():
        print(f"✗ 构建脚本不存在: {build_script}")
        return False
    
    print(f"✓ 构建脚本存在: {build_script}")
    
    # 检查虚拟环境
    venv_dir = BACKEND_DIR / "venv"
    if not venv_dir.exists():
        print(f"✗ 虚拟环境不存在: {venv_dir}")
        print("请先运行: python -m venv Backend_Django/venv")
        return False
    
    print(f"✓ 虚拟环境存在: {venv_dir}")
    
    # 检查Python可执行文件
    if sys.platform == "win32":
        python_exe = venv_dir / "Scripts" / "python.exe"
    else:
        python_exe = venv_dir / "bin" / "python"
    
    if not python_exe.exists():
        print(f"✗ Python可执行文件不存在: {python_exe}")
        return False
    
    print(f"✓ Python可执行文件存在: {python_exe}")
    
    # 检查requirements.txt
    requirements_file = BACKEND_DIR / "requirements.txt"
    if not requirements_file.exists():
        print(f"✗ requirements.txt不存在: {requirements_file}")
        return False
    
    print(f"✓ requirements.txt存在: {requirements_file}")
    
    return True

def test_frontend_config():
    """测试前端配置"""
    print("\n=== 测试前端配置 ===")
    
    # 检查package.json
    package_json = FRONTEND_DIR / "package.json"
    if not package_json.exists():
        print(f"✗ package.json不存在: {package_json}")
        return False
    
    print(f"✓ package.json存在: {package_json}")
    
    # 检查electron-builder配置
    builder_config = FRONTEND_DIR / "electron-builder.json"
    if not builder_config.exists():
        print(f"✗ electron-builder.json不存在: {builder_config}")
        return False
    
    print(f"✓ electron-builder.json存在: {builder_config}")
    
    # 检查Electron脚本
    main_script = FRONTEND_DIR / "electron" / "main.cjs"
    backend_script = FRONTEND_DIR / "electron" / "backend.cjs"
    
    if not main_script.exists():
        print(f"✗ main.cjs不存在: {main_script}")
        return False
    
    if not backend_script.exists():
        print(f"✗ backend.cjs不存在: {backend_script}")
        return False
    
    print(f"✓ Electron脚本存在")
    
    return True

def test_django_setup():
    """测试Django设置"""
    print("\n=== 测试Django设置 ===")
    
    # 检查manage.py
    manage_py = BACKEND_DIR / "manage.py"
    if not manage_py.exists():
        print(f"✗ manage.py不存在: {manage_py}")
        return False
    
    print(f"✓ manage.py存在: {manage_py}")
    
    # 检查settings.py
    settings_py = BACKEND_DIR / "backend_project" / "settings.py"
    if not settings_py.exists():
        print(f"✗ settings.py不存在: {settings_py}")
        return False
    
    print(f"✓ settings.py存在: {settings_py}")
    
    # 检查数据库目录
    db_dir = BACKEND_DIR / "db"
    if not db_dir.exists():
        print(f"! 数据库目录不存在，将在首次运行时创建: {db_dir}")
    else:
        print(f"✓ 数据库目录存在: {db_dir}")
    
    # 检查模型目录
    models_dir = BACKEND_DIR / "models"
    if not models_dir.exists():
        print(f"! 模型目录不存在，将在首次运行时创建: {models_dir}")
    else:
        print(f"✓ 模型目录存在: {models_dir}")
    
    return True

def simulate_build_process():
    """模拟构建过程"""
    print("\n=== 模拟构建过程 ===")
    
    print("1. 检查后端依赖安装...")
    venv_dir = BACKEND_DIR / "venv"
    if sys.platform == "win32":
        pip_exe = venv_dir / "Scripts" / "pip.exe"
    else:
        pip_exe = venv_dir / "bin" / "pip"
    
    if pip_exe.exists():
        try:
            # 检查PyInstaller是否已安装
            result = subprocess.run([str(pip_exe), "show", "pyinstaller"], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                print("✓ PyInstaller已安装")
            else:
                print("! PyInstaller未安装，构建时会自动安装")
        except subprocess.TimeoutExpired:
            print("! pip命令超时，可能需要检查网络连接")
        except Exception as e:
            print(f"! 检查依赖时出错: {e}")
    
    print("2. 检查前端依赖...")
    node_modules = FRONTEND_DIR / "node_modules"
    if node_modules.exists():
        print("✓ 前端依赖已安装")
    else:
        print("! 前端依赖未安装，构建时会自动安装")
    
    print("3. 验证构建配置...")
    # 这里可以添加更多配置验证
    
    return True

def provide_build_instructions():
    """提供构建说明"""
    print("\n=== 构建说明 ===")
    print("要构建自包含应用，请按以下步骤操作：")
    print()
    print("方法1 - 一键构建（推荐）：")
    print("  python build_standalone_app.py")
    print()
    print("方法2 - 手动构建：")
    print("  1. cd Backend_Django")
    print("  2. python build_backend.py")
    print("  3. cd ../Frontend")
    print("  4. npm install")
    print("  5. npm run build")
    print("  6. npm run electron:build:win")
    print()
    print("构建完成后，可执行文件将位于：")
    print("  Frontend/release/win-unpacked/AI Vision App.exe")
    print()

def main():
    """主函数"""
    print("=== AI Vision Platform 构建测试 ===")
    print(f"项目根目录: {PROJECT_ROOT}")
    
    success = True
    
    # 运行各项测试
    if not test_backend_build():
        success = False
    
    if not test_frontend_config():
        success = False
    
    if not test_django_setup():
        success = False
    
    if not simulate_build_process():
        success = False
    
    # 提供构建说明
    provide_build_instructions()
    
    if success:
        print("\n✅ 所有测试通过！可以开始构建自包含应用。")
        return 0
    else:
        print("\n❌ 部分测试失败，请解决上述问题后重试。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
