# AI Vision Platform - 自包含应用打包说明

## 🎯 目标
打包一个完全自包含的应用，用户无需安装Python环境即可直接使用。

## 📋 开发环境要求
开发者需要安装：
- **Python 3.9 或更高版本**
- **Node.js 16 或更高版本**
- **npm 或 yarn**
- **Windows 10 或更高版本**

## 🚀 一键打包（推荐）

### 使用自动化脚本
```powershell
# 在项目根目录运行
python build_standalone_app.py
```

这个脚本会自动完成：
1. 检查环境依赖
2. 安装后端Python依赖
3. 使用PyInstaller构建后端可执行文件
4. 安装前端npm依赖
5. 构建React前端
6. 打包Electron应用

## 🔧 手动打包步骤

### 1. 构建后端可执行文件
```powershell
cd Backend_Django
python build_backend.py
```

### 2. 构建前端并打包
```powershell
cd Frontend
npm install
npm run build
npm run electron:build:win
```

## 📁 打包结果
打包完成后在 `Frontend/release` 目录下：
- `AI Vision App-1.0.0-x64.exe` - 安装程序
- `win-unpacked/AI Vision App.exe` - 便携版本

## 🎯 用户使用方式

### 方式1：安装版本
1. 用户运行 `AI Vision App-1.0.0-x64.exe`
2. 按提示安装
3. 双击桌面快捷方式启动

### 方式2：便携版本
1. 将 `win-unpacked` 文件夹发给用户
2. 用户直接运行 `AI Vision App.exe`

## ✅ 优势
- **完全自包含**：用户无需安装Python环境
- **一键启动**：双击即可运行
- **快速启动**：后端使用编译后的可执行文件，启动更快
- **体积优化**：只包含必要的依赖，避免虚拟环境冗余

## 🔧 故障排除

### 构建失败
1. 确保Python虚拟环境正确安装所有依赖
2. 检查PyInstaller是否正确安装：`pip install pyinstaller`
3. 查看构建日志中的错误信息

### 运行时问题
1. 查看应用日志：`%APPDATA%\ai-vision-platform\app.log`
2. 确保防火墙允许应用访问网络
3. 检查端口8000是否被占用
